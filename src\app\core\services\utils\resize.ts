/**
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE
 */

import { Injectable, NgZone, Renderer2, RendererFactory2, inject } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { auditTime, finalize } from 'rxjs/operators';

const NOOP = (): void => {};

/**
 * Servizio per la gestione degli eventi di resize della finestra/browser.
 * Permette di sottoscriversi agli eventi di resize in modo reattivo.
 */
@Injectable({
  providedIn: 'root',
})
export class ResizeService {
  private ngZone = inject(NgZone);
  private rendererFactory2 = inject(RendererFactory2);

  readonly #resizeSource$ = new Subject<void>();

  #listeners = 0;

  #renderer: Renderer2;

  #disposeHandle = NOOP;

  #handler = (): void => {
    this.ngZone.run(() => {
      this.#resizeSource$.next();
    });
  };

  constructor() {
    this.#renderer = this.rendererFactory2.createRenderer(null, null);
  }

  /**
   * Sottoscrive agli eventi di resize della finestra.
   * @returns Observable che emette ad ogni resize
   */
  subscribe(): Observable<void> {
    this.#registerListener();

    return this.#resizeSource$.pipe(
      auditTime(16),
      finalize(() => this.#unregisterListener()),
    );
  }

  // #unsubscribe(): void {
  //   this.#unregisterListener();
  // }

  #registerListener(): void {
    if (this.#listeners === 0) {
      this.ngZone.runOutsideAngular(() => {
        this.#disposeHandle = this.#renderer.listen(
          'window',
          'resize',
          this.#handler,
        );
      });
    }

    this.#listeners += 1;
  }

  #unregisterListener(): void {
    this.#listeners -= 1;

    if (this.#listeners === 0) {
      this.#disposeHandle();
      this.#disposeHandle = NOOP;
    }
  }
}
