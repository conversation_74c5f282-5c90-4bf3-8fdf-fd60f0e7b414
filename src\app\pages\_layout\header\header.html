<div class="header">
  <nz-page-header class="site-page-header" [nzSubtitle]="tplSubtitle">
    <!--avatar-->
    <nz-avatar
      nz-page-header-avatar
      nzSize="small"
      [nzIcon]="currentMenu()?.icon"
    ></nz-avatar>

    <!--title-->
    <nz-page-header-title>
      <span>
        <a (click)="gotoPage(currentMenu()?.routerLink)">{{
          currentMenu()?.title | translate
        }}</a></span
      >
    </nz-page-header-title>

    <!--subtitle-->
    <ng-template #tplSubtitle>
      @for (item of urls() | slice: 1; track $index) {
        @defer (on timer(400)) {
          <nz-page-header-subtitle>/</nz-page-header-subtitle>
          <nz-page-header-subtitle>
            <a
              class="breadcrumb"
              [ngClass]="{ disabled: $last }"
              (click)="!$last ? gotoSubpage(item) : null"
              >{{
                ($index === 0 && breadcrumbData() ? breadcrumbData() : item)
                  | translate
              }}</a
            >
          </nz-page-header-subtitle>
        }
      }

      <!-- Display current staff in the bookings calendar -->
      @if (
        urls()?.length === 1 && urls()[0] === "bookings" && breadcrumbData()
      ) {
        <nz-page-header-subtitle>/</nz-page-header-subtitle>
        <nz-page-header-subtitle>
          <a class="breadcrumb disabled">{{ breadcrumbData() }}</a>
        </nz-page-header-subtitle>
      }
    </ng-template>

    <!--extra-->
    <nz-page-header-extra>
      <span>
        @switch (currentSection()) {
          @case (currentSectionType.dashboard) {
            <app-dashboard-header></app-dashboard-header>
          }
          @case (currentSectionType.productList) {
            <app-products-header></app-products-header>
          }
          @default {}
        }
      </span>
    </nz-page-header-extra>
  </nz-page-header>
  <nz-divider class="divider"></nz-divider>
</div>
