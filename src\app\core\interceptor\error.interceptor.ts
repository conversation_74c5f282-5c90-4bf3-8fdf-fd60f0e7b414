import { HttpEvent, HttpHandlerFn, HttpRequest } from '@angular/common/http';
import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '@core/services/http/auth';
import { HeaderService } from '@core/services/utils/header';
import { MessageService } from '@core/services/utils/message';
import { log } from '@core/utils/logger';
import { StringUtils } from '@core/utils/string';
import { currentSectionType } from '@models/enums/current-section';
import { errorType } from '@models/enums/errors';
import { IBaseErrorResponse } from '@models/interfaces/base-response';
import {
  BehaviorSubject,
  catchError,
  filter,
  finalize,
  Observable,
  switchMap,
  take,
} from 'rxjs';

export function errorInterceptor(
  req: HttpRequest<unknown>,
  next: HttpHandlerFn,
): Observable<HttpEvent<unknown>> {
  const authService = inject(AuthService);
  const messageService = inject(MessageService);
  const router = inject(Router);
  const headerService = inject(HeaderService);
  const MANUAL_ERRORS: string[] = [
    StringUtils.getEnumKeyByEnumValue(errorType, errorType.E1001),
  ] as const;

  let refreshTokenInProgress: boolean = false;
  let refreshTokenSubject = new BehaviorSubject(false);

  const manageClientError = (code: number) => {
    log('manageClientError', code);
    switch (code) {
      case 401:
        headerService.setCurrentSection(currentSectionType.noSection);
        log('MANAGE CLIENT ERROR: ', code);
        authService.logout().subscribe();
        break;
      case 403:
        router.navigateByUrl(`/error?code=${code}`);
        break;
      default:
        log('MANAGE CLIENT ERROR: ', code);
        break;
    }
  };
  const manageServerError = (code: number) => {
    switch (code) {
      case 500:
        headerService.setCurrentSection(currentSectionType.noSection);
        const returnUrl = router.url;
        router.navigateByUrl(`/error?code=${code}&returnUrl=${returnUrl}`);
        break;
      default:
        log('MANAGE SERVER ERROR: ', code);
        break;
    }
  };

  const tryRefreshToken = () => {
    if (refreshTokenInProgress) {
      return refreshTokenSubject.pipe(
        filter((result) => !!result),
        take(1),
        switchMap(() => next(req)),
      );
    } else {
      refreshTokenInProgress = true;
      refreshTokenSubject.next(false);
      return authService.refreshToken().pipe(
        switchMap((token) => {
          refreshTokenSubject.next(true);
          return next(req);
        }),
        finalize(() => {
          refreshTokenInProgress = false;
        }),
      );
    }
  };

  return next(req).pipe(
    catchError((err) => {
      const error = err.error as IBaseErrorResponse;

      if (err.status === 449) {
        return tryRefreshToken();
      }
      if (err.status >= 400 && err.status < 500) {
        if (err.status !== 401 && !MANUAL_ERRORS.includes(error.errorCode)) {
          messageService.addErrorMessage(
            'ERRORS.' + errorType[error.errorCode],
          );
        }
        manageClientError(err.status);
      }
      if (err.status >= 500 && err.status < 600) {
        manageServerError(err.status);
        messageService.addErrorMessage('Server Error');
      }
      throw err;
    }),
  );
}
