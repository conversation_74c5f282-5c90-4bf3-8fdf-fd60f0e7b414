import {
  languageCodeType,
  languageCountryCodeType,
} from '@core/services/utils/language';
import { ISettings } from '@models/interfaces/configuration';

export const environment: ISettings = {
  production: `${process.env['PRODUCTION']}` === 'true',
  api: {
    base: `${process.env['BASE_API_URL']}`,
    auth: `${process.env['BASE_API_URL']}/api/v1/auth/admin`,
    admin: `${process.env['BASE_API_URL']}/api/v1/admin`,
    notifications: `${process.env['BASE_API_URL']}/api/v1/notifications`,
    products: `${process.env['BASE_API_URL']}/api/v1/products`,
    categories: `${process.env['BASE_API_URL']}/api/v1/categories`,
    collections: `${process.env['BASE_API_URL']}/api/v1/collections`,
  },
  languages: [
    {
      name: 'Italiano',
      code: languageCodeType.it,
      countryCode: languageCountryCodeType.IT,
      enabled: true,
      default: true,
    },
    {
      name: 'English',
      code: languageCodeType.en,
      countryCode: languageCountryCodeType.GB,
      enabled: true,
    },
  ],
  sections: [
    {
      level: 0,
      title: 'dashboard',
      id: 'M_0',
      icon: 'home',
      rolePermission: [],
      routerLink: 'dashboard',
    },
    {
      level: 0,
      title: 'products',
      id: 'M_1',
      icon: 'home',
      rolePermission: [],
      routerLink: 'products',
    },
  ],
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.
