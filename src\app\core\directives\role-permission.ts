import {
  afterNextRender,
  Directive,
  effect,
  inject,
  input,
  TemplateRef,
  ViewContainerRef,
} from '@angular/core';
import { AuthService } from '@core/services/http/auth';
import { roleType } from '@models/enums/role';

@Directive({
  selector: '[rolePermission]',
  standalone: true,
})
export class RolePermissionDirective {
  // SERVICES
  private authService = inject(AuthService);
  private templateRef = inject(TemplateRef<any>);
  private viewContainer = inject(ViewContainerRef);

  rolePermission = input.required<roleType[]>();
  user = inject(AuthService).user;
  userEffect = effect(() => {
    if (this.user()) this.checkStaffPermission();
  });

  constructor() {
    afterNextRender(() => this.checkStaffPermission());
  }

  checkStaffPermission() {
    const userRole = this.authService.user()?.role;

    if (this.rolePermission().includes(userRole)) {
      this.viewContainer.createEmbeddedView(this.templateRef);
    } else {
      this.viewContainer.clear();
    }
  }
}
