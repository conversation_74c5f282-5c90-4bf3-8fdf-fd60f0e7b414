import { UpperCasePipe } from '@angular/common';
import { Component, DestroyRef, OnInit, signal, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { trackEvent } from '@aptabase/web';
import { AuthService } from '@core/services/http/auth';
import { MessageService } from '@core/services/utils/message';
import { CustomValidators } from '@core/validators/custom.validator';
import { TranslateModule } from '@ngx-translate/core';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { InputPasswordComponent } from '@shared/inputs/input-password/input-password';
import { NzFormDirective } from 'ng-zorro-antd/form';
import { NzIconDirective } from 'ng-zorro-antd/icon';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    InputPasswordComponent,
    FormsModule,
    ReactiveFormsModule,
    NzFormDirective,
    TranslateModule,
    UpperCasePipe,
    NzIconDirective,
    SimpleButtonComponent,
  ],
  templateUrl: './activate-account.html',
  styleUrl: './activate-account.less',
})
export class ActivateAccountComponent implements OnInit {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private authService = inject(AuthService);
  private route = inject(ActivatedRoute);
  private messageService = inject(MessageService);
  private destroyRef = inject(DestroyRef);

  protected baseForm!: FormGroup;
  private token!: string;
  private tenantId!: string;
  protected isLoading = signal<boolean>(true);

  constructor() {
    trackEvent('activate_account_page');
  }

  ngOnInit(): void {
    this.route.queryParams
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((params) => {
        this.token = params['token'];

        if (!this.token && !this.tenantId) {
          this.router.navigate(['/']);
        } else {
          this.isLoading.set(false);
          this.initForm();
        }
      });
  }

  initForm() {
    this.baseForm = this.fb.group(
      {
        password: [
          '',
          [
            Validators.required,
            Validators.minLength(9),
            Validators.maxLength(50),
          ],
        ],
        confirmPassword: ['', [Validators.required, Validators.minLength(9)]],
      },
      {
        validators: CustomValidators.confirmPasswordValidator(
          'password',
          'confirmPassword',
        ),
      },
    );
  }

  onBackToLoginClick() {
    this.router.navigateByUrl('/auth/login');
  }

  onActivateAccountClick() {
    this.isLoading.set(true);
    const { password } = this.baseForm.value;
    this.authService
      .activateAccount(password, this.token)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe({
        next: () => {
          this.messageService.addSuccessMessage('ACTIVATE.success');
          this.router.navigateByUrl('/auth/login');
        },
        error: () => {
          this.isLoading.set(false);
        },
      });
  }
}
