<app-table
  #productTable
  [tableLayoutSettings]="tableLayoutSettings"
  [loading]="loading"
  [data]="data"
  [refresh]="refreshData"
  (onRefreshTimerTick)="onQueryChange(tableQueryRequest)"
  [tableId]="'_productsTable'"
  (onQueryChange)="onQueryChange($event)"
>
</app-table>

<ng-template #tplId let-element="element">
  <a
    class="d-block"
    ellipsis
    [tooltipTitle]="element"
    (click)="goToDetail(element)"
    >{{ element }}</a
  >
</ng-template>

<ng-template #tplName let-element="element" let-row="row">
  {{ element }} / {{ row.variantName }}
</ng-template>

<ng-template #tplIsOnline let-element="element">
  <app-tag-boolean-status
    [value]="element"
    [trueText]="'online'"
    [falseText]="'offline'"
  ></app-tag-boolean-status>
</ng-template>

<ng-template #tplTimestamp let-element="element">
  {{ element | dateFormat | nullValue }}
</ng-template>

<ng-template #tplImage let-element="element" let-row="row">
  <nz-avatar-group class="d-inline pointer">
    @for (el of element; track $index) {
      <nz-avatar
        nz-tooltip
        [nzTooltipTitle]="el.name"
        [nzSrc]="el.url"
        [nzSize]="'small'"
        (click)="onImageClick(element)"
      ></nz-avatar>
    }
  </nz-avatar-group>
</ng-template>

<ng-template #tplOptions let-row="row">
  <button
    nz-button
    nzType="default"
    nz-popover
    [nzPopoverTitle]="'PRODUCTS.options' | translate"
    nzPopoverTrigger="click"
    [nzPopoverContent]="tplOptionsContent"
    >{{ 'PRODUCTS.showOptions' | translate }}</button
  >
  <ng-template #tplOptionsContent>
    <div class="grid">
      <div class="name">{{ 'PRODUCTS.name' | translate }}</div>
      <div class="price">{{ 'PRODUCTS.price' | translate }}</div>
      <div class="quantity">{{ 'PRODUCTS.quantity' | translate }}</div>
      @for (item of row.options; track $index) {
        <div class="option name">
          <span nz-icon nzType="client-ui:size" style="margin-left: -3px"></span>
          {{ item.sizeType }}</div
        >
        <div class="option price">
          <span
            nz-icon
            nzType="client-ui:price"
            style="margin-right: 1rem"
          ></span>
          @if (item.discount) {
            <app-badge-discount
              [price]="getFinalPrice(item.discount, item.price)"
              style="margin-right: -4px"
            ></app-badge-discount>
          } @else {
            {{ item.price | currency: 'EUR' }}
          }
        </div>
        <div class="option quantity"
          ><span nz-icon nzType="client-ui:quantity" class="icon"></span
          >{{ item.stockQuantity }}
        </div>
      }
    </div>
  </ng-template>
</ng-template>

<ng-template #tplColor let-element="element">
  @if (element) {
    <nz-tag [nzColor]="element.hex" class="border-radius">
      <span
        [ngStyle]="{
          color: ColorUtils.whiteOrBlackByBackground(element.hex)
        }"
        >{{ element.name }}</span
      ></nz-tag
    >
  } @else {
    <ng-container *ngTemplateOutlet="tplNullValue"></ng-container>
  }
</ng-template>

<ng-template #tplNullValue>
  {{ null | nullValue }}
</ng-template>
