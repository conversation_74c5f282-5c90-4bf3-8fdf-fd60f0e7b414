import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivateFn,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { AuthService } from '@core/services/http/auth';

export const authGuard: CanActivateFn = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot,
) => {
  const router = inject(Router);
  const user = inject(AuthService).user;
  const rolePermission = route.data['rolePermission'];
  const isLoggedIn = localStorage.getItem('user_id') ? true : false;
  if (!isLoggedIn) return router.navigateByUrl('/auth/login');

  const hasPermission = rolePermission.includes(user()?.role);

  return hasPermission ? true : router.navigateByUrl('/');
};
