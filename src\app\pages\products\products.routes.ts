import { Routes } from '@angular/router';
import { crudActionType, customModeType } from '@models/enums/crud-action-type';
import { ProductListComponent } from '@pages/products/product-list/product-list';
import { CreateUpdateVariantComponent } from './product-create-update/create-update-variant/create-update-variant';
import { ProductCreateUpdateComponent } from './product-create-update/product-create-update';

export const routes: Routes = [
  {
    path: '',
    component: ProductListComponent,
  },
  {
    path: 'create',
    component: ProductCreateUpdateComponent,

    data: { crudType: crudActionType.create },
    children: [
      {
        path: '',
        component: CreateUpdateVariantComponent,
      },
    ],
  },
  {
    path: ':id/variant',
    component: ProductCreateUpdateComponent,
    data: {
      crudType: crudActionType.create,
      customMode: customModeType.createVariant,
    },
    children: [
      {
        path: '',
        component: CreateUpdateVariantComponent,
      },
    ],
  },
  {
    path: ':id',
    component: ProductCreateUpdateComponent,
    data: { crudType: crudActionType.update },
    children: [
      {
        path: '',
        component: CreateUpdateVariantComponent,
      },
    ],
  },
];
