import { ILanguage } from '@core/services/utils/language';
import { ISection } from './menu';

export interface ISettings {
  production: boolean;
  api: Api;
  languages: ILanguage[];
  sections: ISection[];
}

type Api = Partial<Record<apiType, string>>;

export enum apiType {
  base = 'base',
  auth = 'auth',
  admin = 'admin',
  notifications = 'notifications',
  products = 'products',
  categories = 'categories',
  collections = 'collections',
}
