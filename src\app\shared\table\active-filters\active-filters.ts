import { Ng<PERSON>tyle, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, OnInit, input, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DateFormatPipe, dateFormatType } from '@core/pipes/date.pipe';
import { DestroyService } from '@core/services/utils/destroy';
import { CheckUtils } from '@core/utils/check';
import { TranslateModule } from '@ngx-translate/core';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzPopoverDirective } from 'ng-zorro-antd/popover';
import { NzTagComponent } from 'ng-zorro-antd/tag';
import { NzTooltipDirective } from 'ng-zorro-antd/tooltip';
import { takeUntil } from 'rxjs';
import { TableConfigService } from '../table-config.service';
import { TableQueryService } from '../table-query.service';
import { ITableColumn } from '../types/table.column';
import { ITableFilterItem } from '../types/table.filter';
import { tableFilterType } from './../types/table.filter';

@Component({
  selector: 'app-active-filters',
  standalone: true,
  imports: [
    TranslateModule,
    NgStyle,
    FormsModule,
    NgTemplateOutlet,
    NzIconDirective,
    NzTagComponent,
    NzButtonComponent,
    NzTooltipDirective,
    NzPopoverDirective,
  ],
  providers: [DestroyService],
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: './active-filters.html',
  styleUrl: './active-filters.less',
})
export class ActiveFiltersComponent implements OnInit, OnChanges {
  private tableQueryService = inject(TableQueryService);
  private tableConfigService = inject(TableConfigService);
  private dateFormatterPipe = inject(DateFormatPipe);
  private destroy$ = inject(DestroyService);
  private cdr = inject(ChangeDetectorRef);

  readonly columns = input<ITableColumn[]>(undefined);
  /** Input max number of active filters tag before "more tag" */
  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() maxActiveFilterTagCount: number = 3;

  public closeAllVisible: boolean = true;
  public filters: Array<ITableFilterItem> = [];
  public filterPopoverVisible: boolean = false;

  ngOnInit(): void {
    this.tableConfigService.activeFilter$
      .pipe(takeUntil(this.destroy$))
      .subscribe((activeFilters) => {
        this.filters = [...activeFilters];
        this.cdr.detectChanges();
      });
  }

  ngOnChanges(): void {
    const columns = this.columns();
    if (columns) {
      let col = columns.find((col) => col.readOnly || col.filterUnclosable);
      this.closeAllVisible = !col;
    }
  }

  /**
   * Get tag mode
   * @param filter
   * @returns "default" | "closeable" | "checkable"
   */
  public getTagMode(filter: any): 'default' | 'closeable' | 'checkable' {
    let result: 'default' | 'closeable' | 'checkable';
    let col = this.columns().find((col) => col.id === filter.key);
    result = col.readOnly || col.filterUnclosable ? 'default' : 'closeable';
    return result;
  }

  /**
   * Close Filter
   * @param filter
   */
  public closeFilter(filter: { key: any; value: any }): void {
    this.tableQueryService.deleteRawFilter(filter.key);
  }

  public getFilterTitleByKey(key: string) {
    let result = this.columns().find((col) => col.id === key);
    if (result) return result.title;
    return key;
  }

  /**
   * Close all filters
   */
  public closeAllFilters(): void {
    this.tableQueryService.resetRawFilters();
  }

  /**
   * Display Filter Value
   * @param filter
   * @returns values
   */
  displayFilterValue(filter: ITableFilterItem): string {
    let result: string;
    switch (filter.filterType) {
      case tableFilterType.date:
        var colShowTime: boolean = this.columns().find(
          (col) => col.id === filter.key,
        )?.dateFilterShowTime;
        colShowTime = CheckUtils.isNullUndefinedOrEmpty(colShowTime)
          ? true
          : colShowTime;
        var dateFormat = colShowTime ? null : 'date';
        result =
          this.dateFormatterPipe.transform(
            filter.value[0],
            <dateFormatType>dateFormat,
          ) +
          '<br>' +
          this.dateFormatterPipe.transform(
            filter.value[1],
            <dateFormatType>dateFormat,
          );
        break;
      case tableFilterType.singleDate:
        var colShowTime: boolean = this.columns().find(
          (col) => col.id === filter.key,
        )?.dateFilterShowTime;
        colShowTime = CheckUtils.isNullUndefinedOrEmpty(colShowTime)
          ? true
          : colShowTime;
        dateFormat = colShowTime ? null : 'date';
        result = this.dateFormatterPipe.transform(
          filter.value,
          <dateFormatType>dateFormat,
        );
        break;
      default:
        if (CheckUtils.isNotNilObject(filter.value)) {
          switch (true) {
            case CheckUtils.isArray(filter.value.value):
              if (CheckUtils.isDateRange(filter.value.value)) {
                result =
                  this.dateFormatterPipe.transform(filter.value.value[0]) +
                  '<br>' +
                  this.dateFormatterPipe.transform(filter.value.value[1]);
              } else {
                result =
                  filter.value.value[0].toString() +
                  ', ' +
                  filter.value.value[1].toString();
              }
              break;
            case filter.value.type === 'timestamp':
              result = this.dateFormatterPipe.transform(filter.value.value);
              break;
            default:
              result = filter.value.value.toString();
              break;
          }
          break;
        } else {
          let translateFn = this.columns().find(
            (col) => col.id === filter.key,
          )?.translateFilterFn;
          if (!!translateFn) {
            let newResult = translateFn(filter.value);
            result = newResult.toString();
          } else {
            result = filter.value.toString();
          }
        }
        break;
    }
    return result;
  }
}
