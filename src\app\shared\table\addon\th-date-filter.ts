import {
  ChangeDetectionStrategy,
  Component,
  inject,
  Input,
  output,
  SimpleChanges,
  viewChild
} from '@angular/core';
import { FormsModule } from '@angular/forms';

import { LanguageService } from '@core/services/utils/language';
import { CheckUtils } from '@core/utils/check';
import { TranslateModule } from '@ngx-translate/core';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import {
  NzDatePickerComponent,
  NzRangePickerComponent,
} from 'ng-zorro-antd/date-picker';
import { NzSpaceComponent } from 'ng-zorro-antd/space';
import { ITableColumn } from '../types/table.column';
import { dateRangeNumberType, dateRangeType } from '../types/table.filter';

@Component({
  selector: 'th-date-filter',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NzDatePickerComponent,
    NzRangePickerComponent,
    FormsModule,
    NzSpaceComponent,
    TranslateModule,
    NzButtonComponent,
  ],
  template: `
    @if (column.hasSingleDateFilter) {
      <nz-date-picker
        #datePicker
        [nzShowTime]="showTime"
        nzFormat="yyyy-MM-dd HH:mm"
        (nzOnOpenChange)="onDatePickerOpen()"
        [nzAllowClear]="column.filterUnclosable ? false : true"
        [ngModel]="searchValue"
        (ngModelChange)="onModelChange($event)"
      >
      </nz-date-picker>
    }
    @if (column.hasDateFilter) {
      <nz-range-picker
        #datePicker
        [nzShowTime]="showTime"
        [nzAllowClear]="column.filterUnclosable ? false : true"
        (nzOnOpenChange)="onDatePickerOpen()"
        nzFormat="yyyy-MM-dd HH:mm"
        [nzRenderExtraFooter]="
          column.dateFilterShortcuts ? tplRangeFooter : null
        "
        [ngModel]="searchValue"
        (ngModelChange)="onModelChange($event)"
      >
      </nz-range-picker>
    }
    <ng-template #tplRangeFooter>
      <div style="overflow-x: auto; width: 450px;">
        <nz-space style="padding: 0 8px">
          <button
            *nzSpaceItem
            nz-button
            nzSize="small"
            nzType="primary"
            (click)="setShortcutDate(dateRangeType.last24Hour)"
          >
            {{ 'TABLE.last24Hour' | translate }}
          </button>
          <button
            *nzSpaceItem
            nz-button
            nzSize="small"
            nzType="primary"
            (click)="setShortcutDate(dateRangeType.lastWeek)"
          >
            {{ 'TABLE.lastWeek' | translate }}
          </button>
          <button
            *nzSpaceItem
            nz-button
            nzSize="small"
            nzType="primary"
            (click)="setShortcutDate(dateRangeType.lastMonth)"
          >
            {{ 'TABLE.lastMonth' | translate }}
          </button>
          <button
            *nzSpaceItem
            nz-button
            nzSize="small"
            nzType="primary"
            (click)="setShortcutDate(dateRangeType.lastYear)"
          >
            {{ 'TABLE.lastYear' | translate }}
          </button>
        </nz-space>
      </div>
    </ng-template>
  `,
})
export class ThDateFilterComponent {
  // SERVICES
  private languageService = inject(LanguageService);
  readonly datePicker = viewChild<NzDatePickerComponent>('datePicker');

  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() column: ITableColumn;
  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input() searchValue: Date | Date[];

  readonly onDateChange = output<Date | Date[]>();
  readonly onResetDate = output<any>();

  public dateRangeType = dateRangeType;
  public showTime: boolean = true;

  constructor() {}

  ngOnChanges(changes: SimpleChanges): void {
    const { column } = changes;
    if (column) {
      this.showTime = !CheckUtils.isNullUndefinedOrEmpty(
        this.column.dateFilterShowTime,
      )
        ? this.column.dateFilterShowTime
        : true;
    }
  }

  onModelChange(event: Date | Date[]) {
    this.searchValue = event;
    if (CheckUtils.isNullUndefinedOrEmpty(this.searchValue)) {
      this.onResetDate.emit(null);
    } else {
      if (!this.showTime) this.removeTime(this.searchValue);
      this.onDateChange.emit(this.searchValue);
    }
    this.datePicker().close();
  }

  private removeTime(date: Date | Date[]) {
    switch (true) {
      case this.column.hasSingleDateFilter:
        return new Date((<Date>date).setHours(0, 0, 0, 0));
      case this.column.hasDateFilter:
      default:
        return [
          new Date((<Date>date[0]).setHours(0, 0, 0, 0)),
          new Date((<Date>date[1]).setHours(23, 59, 59, 999)),
        ];
    }
  }

  /**
   * Cannot generate summary
   * @param {dateRangeType} type - dateRangeType
   */
  public setShortcutDate(type: dateRangeType) {
    let date: Date[];
    switch (type) {
      case dateRangeType.last24Hour:
        let last24h = Date.now() - dateRangeNumberType.oneDay;
        date = [new Date(last24h), new Date(Date.now())];
        break;
      case dateRangeType.lastWeek:
        let lastWeek = Date.now() - dateRangeNumberType.oneWeek;
        date = [new Date(lastWeek), new Date(Date.now())];
        break;
      case dateRangeType.lastMonth:
        let lastMonth = Date.now() - dateRangeNumberType.oneMonth;
        date = [new Date(lastMonth), new Date(Date.now())];
        break;
      case dateRangeType.lastYear:
        let lastYear = Date.now() - dateRangeNumberType.oneYear;
        date = [new Date(lastYear), new Date(Date.now())];
        break;
    }
    this.onModelChange(date);
  }

  onDatePickerOpen() {
    this.datePicker().nzLocale.lang.ok =
      this.languageService.instantTranslate('TABLE.search');
  }
}
