import { Ng<PERSON><PERSON>, Ng<PERSON>tyle } from '@angular/common';
import { Component, Input, input, output, inject } from '@angular/core';
import { DestroyService } from '@core/services/utils/destroy';
import { ResizeService } from '@core/services/utils/resize';
import { TranslateModule } from '@ngx-translate/core';
import {
  NzButtonComponent,
  NzButtonSize,
  NzButtonType,
} from 'ng-zorro-antd/button';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzTooltipDirective } from 'ng-zorro-antd/tooltip';
import { takeUntil } from 'rxjs';

@Component({
  selector: 'app-simple-button',
  standalone: true,
  providers: [DestroyService],
  imports: [
    NzButtonComponent,
    NzIconDirective,
    TranslateModule,
    NzTooltipDirective,
    NgStyle,
    NgClass,
  ],
  templateUrl: './simple-button.html',
  styleUrl: './simple-button.less',
})
export class SimpleButtonComponent {
  private resizeService = inject(ResizeService);
  private destroy$ = inject(DestroyService);

  // TODO: Skipped for migration because:
  //  Your application code writes to the input. This prevents migration.
  @Input() title: string;
  // TODO: Skipped for migration because:
  //  This input is used in a control flow expression (e.g. `@if` or `*ngIf`)
  //  and migrating would break narrowing currently.
  @Input() icon: string;
  readonly type = input<NzButtonType>('primary');
  readonly iconOnly = input<boolean>(false);
  readonly danger = input<boolean>(false);
  readonly breakpoint = input<number>(1200);
  readonly autoMinify = input<boolean>(true);
  readonly disabled = input<boolean>(undefined);
  readonly tooltipPlacement = input<
    | 'top'
    | 'left'
    | 'right'
    | 'bottom'
    | 'topLeft'
    | 'topRight'
    | 'bottomLeft'
    | 'bottomRight'
    | 'leftTop'
    | 'leftBottom'
    | 'rightTop'
    | 'rightBottom'
  >('top');
  readonly style = input<{
    [key: string]: any;
  }>(undefined);
  readonly iconStyle = input<{
    [key: string]: any;
  }>(undefined);
  readonly iconTheme = input<'outline' | 'fill' | 'twotone'>('outline');
  readonly size = input<NzButtonSize>('default');

  readonly onButtonClick = output<void>();

  protected minified: boolean = false;

  constructor() {
    this.resizeService
      .subscribe()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.checkIfMinified();
      });
  }

  ngOnInit(): void {
    this.checkIfMinified();
  }

  private checkIfMinified() {
    if (this.iconOnly()) {
      this.minified = true;
      this.title = null;
    } else {
      if (this.autoMinify())
        this.minified = window.innerWidth < this.breakpoint();
    }
  }
}
