import { ChangeDetectorRef, Directive, Input, Iterable<PERSON><PERSON><PERSON><PERSON><PERSON>ord, Iterable<PERSON>iffer, IterableDiffers, TemplateRef, ViewContainerRef, ViewRef, input, inject } from '@angular/core';

@Directive({
  standalone: true,
  selector: '[forOf]'
})
export class ForOfDirective {
  private templateRef = inject<TemplateRef<any>>(TemplateRef);
  private viewContainer = inject(ViewContainerRef);
  private differs = inject(IterableDiffers);
  private cdr = inject(ChangeDetectorRef);

  private items: any[] = [];
  private viewRefsMap: Map<any, ViewRef> = new Map<any, ViewRef>();
  private _diffrence: IterableDiffer<any> | undefined;

  // TODO: Skipped for migration because:
  //  Accessor inputs cannot be migrated as they are too complex.
  @Input('forOf')
  public set forOf(items: any) {
    this.items = items;
    if (items) {
      this._diffrence = this.differs.find(items).create();
    }
    //Clear any existing items
    this.viewContainer.clear();
  }

  public readonly itemsAtOnce = input<number>(20, { alias: "forItemsAtOnce" });

  public readonly intervalLength = input<number>(500, { alias: "forIntervalLength" });

  public ngDoCheck(): void {
    if (this._diffrence) {
      const changes = this._diffrence.diff(this.items);
      if (changes) {
        const itemsAdded: any[] = [];
        changes.forEachAddedItem(item => {
          itemsAdded.push(item);
        });

        this.progressiveRender(itemsAdded);

        changes.forEachRemovedItem(item => {
          const mapView = this.viewRefsMap.get(item.item) as ViewRef;
          const viewIndex = this.viewContainer.indexOf(mapView);
          this.viewContainer.remove(viewIndex);
          this.viewRefsMap.delete(item.item);
        });
      }
    }
  }

  private progressiveRender(items: IterableChangeRecord<any>[]) {
    let interval: any;
    let start = 0;
    let end = start + this.itemsAtOnce();
    if (end > items.length) {
      end = items.length;
    }
    this.renderItems(items, start, end);

    interval = setInterval(() => {
      start = end;
      end = start + this.itemsAtOnce();
      if (end > items.length) {
        end = items.length;
      }
      this.renderItems(items, start, end);
      if (start >= items.length) {
        clearInterval(interval);
      }
      this.cdr.detectChanges();
    }, this.intervalLength());
  }

  private renderItems(
    items: IterableChangeRecord<any>[],
    start: number,
    end: number
  ) {
    items.slice(start, end).forEach(item => {
      const embeddedView = this.viewContainer.createEmbeddedView(
        this.templateRef,
        {
          $implicit: item.item
        },
        item.currentIndex || 0
      );
      this.viewRefsMap.set(item.item, embeddedView);
    });
  }
}
