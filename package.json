{"name": "cantina-di-venosa", "version": "0.1.6", "scripts": {"ng": "ng", "build": "ng build --configuration prod", "watch": "ng build --watch --configuration dev", "build:prod": "ng build --configuration prod && npm run post-build", "format": "npx prettier --write .", "post-build": "gulp", "start": "NODE_ENV=dev ng serve --configuration=dev", "start:dev": "NODE_ENV=dev ng serve --configuration dev", "start:local": "NODE_ENV=local ng serve --configuration local", "start:mock": "NODE_ENV=mock ng serve --configuration mock", "e2e": "npx cypress open", "test": "ng test"}, "prettier": {"overrides": [{"files": "*.html", "options": {"parser": "angular"}}]}, "private": true, "dependencies": {"@angular/common": "^20.1.0", "@angular/compiler": "^20.1.0", "@angular/core": "^20.1.0", "@angular/forms": "^20.1.0", "@angular/platform-browser": "^20.1.0", "@angular/router": "^20.1.0", "@aptabase/web": "^0.4.3", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "dotenv": "^17.2.0", "dotenv-webpack": "^8.1.1", "ng-zorro-antd": "^20.0.0", "rxjs": "~7.8.0", "socket.io-client": "^4.8.1", "tslib": "^2.3.0"}, "devDependencies": {"@angular-builders/custom-webpack": "^20.0.0", "@angular/build": "^20.1.0", "@angular/cli": "^20.1.0", "@angular/compiler-cli": "^20.1.0", "@types/jasmine": "~5.1.0", "eslint": "^9.31.0", "eslint-plugin-import": "^2.32.0", "gulp": "^5.0.0", "gulp-clean": "^0.4.0", "gulp-purgecss": "^5.0.0", "gulp-uglify": "^3.0.2", "jasmine-core": "~5.8.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "less": "^4.3.0", "less-loader": "^12.3.0", "prettier": "^3.6.2", "typescript": "~5.8.2", "webpack": "^5.100.2"}, "overrides": {"rollup": "npm:@rollup/wasm-node"}}