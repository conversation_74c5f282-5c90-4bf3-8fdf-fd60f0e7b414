import { HttpClient } from '@angular/common/http';
import { effect, Injectable, signal, untracked, inject } from '@angular/core';
import { environment } from '@env/environment';
import { socketActionTypeResponse } from '@models/enums/socket';
import { IBaseResponse } from '@models/interfaces/base-response';
import { INotification } from '@models/interfaces/notification-boutique';
import { NzMessageService } from 'ng-zorro-antd/message';
import { tap } from 'rxjs';
import { SocketService } from './socket';

/**
 * Servizio per la gestione delle notifiche in tempo reale e da API.
 * Riceve notifiche via socket, mostra messaggi e gestisce lo stato locale.
 */
@Injectable({
  providedIn: 'root',
})
export class NotificationService {
  private socketService = inject(SocketService);
  private http = inject(HttpClient);
  private message = inject(NzMessageService);

  private _notifications = signal<INotification[]>([]);
  public notifications = this._notifications.asReadonly();
  socketReadyEffect = effect(() => {
    if (this.socketService.isSocketReady()) {
      untracked(() => this.initNotificationFromSocket());
    }
  });
  private _baseNotificationApi = environment.api.notifications;

  /**
   * Inizializza la ricezione delle notifiche in tempo reale tramite socket.
   * @returns void
   */
  initNotificationFromSocket(): void {
    this.socketService.socket.on(
      socketActionTypeResponse.notifications,
      (data: INotification) => {
        this._notifications.update((prev) => [data, ...prev]);
        // Riproduci il suono solo se l'utente ha dato il consenso
        if (localStorage.getItem('notificationSoundConsent') === 'true') {
          try {
            const audio = new Audio('assets/sounds/notification.mp3');
            audio.volume = 0.5;
            audio.play().catch(() => {});
          } catch (e) {
            // Silenzia eventuali errori di creazione Audio
          }
        }
      },
    );
  }

  /**
   * Recupera tutte le notifiche tramite API e aggiorna lo stato locale.
   * @returns Observable con la lista delle notifiche
   */
  getNotifications() {
    return this.http
      .get<IBaseResponse<INotification[]>>(`${this._baseNotificationApi}`)
      .pipe(tap((res) => this._notifications.set(res.data)));
  }

  /**
   * Rimuove una notifica tramite API e aggiorna lo stato locale.
   * @param id ID della notifica da rimuovere
   * @returns void
   */
  onRemoveNotification(id: string): void {
    this.http
      .delete<IBaseResponse<void>>(`${this._baseNotificationApi}/${id}`)
      .subscribe({
        next: () => {
          this._notifications.update((prev) =>
            prev.filter((data) => id !== data.id),
          );
        },
      });
  }

  /**
   * Rimuove tutte le notifiche tramite API e aggiorna lo stato locale.
   * @returns void
   */
  clearNotifications(): void {
    this.http
      .delete<IBaseResponse<void>>(`${this._baseNotificationApi}`)
      .subscribe({
        next: () => {
          this._notifications.set([]);
        },
      });
  }

  /**
   * Mostra un messaggio di successo.
   * @param message Testo del messaggio
   * @returns void
   */
  success(message: string): void {
    this.message.success(message);
  }

  /**
   * Mostra un messaggio di errore.
   * @param message Testo del messaggio
   * @returns void
   */
  error(message: string): void {
    this.message.error(message);
  }

  /**
   * Mostra un messaggio di warning.
   * @param message Testo del messaggio
   * @returns void
   */
  warning(message: string): void {
    this.message.warning(message);
  }

  /**
   * Mostra un messaggio informativo.
   * @param message Testo del messaggio
   * @returns void
   */
  info(message: string): void {
    this.message.info(message);
  }
}
