import { UpperCasePipe } from '@angular/common';
import { Component, signal, inject } from '@angular/core';
import {
  <PERSON><PERSON><PERSON>er,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { trackEvent } from '@aptabase/web';
import { AuthService } from '@core/services/http/auth';
import { CustomValidators } from '@core/validators/custom.validator';
import { TranslateModule } from '@ngx-translate/core';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { InputGenericComponent } from '@shared/inputs/input-generic/input-generic';
import { InputPasswordComponent } from '@shared/inputs/input-password/input-password';
import { NzDividerComponent } from 'ng-zorro-antd/divider';
import { NzFormDirective } from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { NzTypographyModule } from 'ng-zorro-antd/typography';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    InputGenericComponent,
    InputPasswordComponent,
    FormsModule,
    ReactiveFormsModule,
    NzFormDirective,
    NzSpinComponent,
    TranslateModule,
    UpperCasePipe,
    NzColDirective,
    NzRowDirective,
    NzTypographyModule,
    SimpleButtonComponent,
    NzDividerComponent,
  ],
  templateUrl: './register.html',
  styleUrl: './register.less',
})
export class RegisterComponent {
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private router = inject(Router);

  protected registerForm: FormGroup<{
    email: FormControl<string>;
    password: FormControl<string>;
    confirmPassword: FormControl<string>;
    name: FormControl<string>;
    surname: FormControl<string>;
  }>;
  protected registerError = signal<boolean>(false);
  protected isLoading = signal<boolean>(false);

  constructor() {
    trackEvent('register_page');
    this.initForm();
  }

  initForm() {
    this.registerForm = this.fb.group(
      {
        email: [
          '',
          {
            validators: [
              Validators.required,
              Validators.minLength(5),
              Validators.maxLength(50),
              Validators.pattern(CustomValidators.emailRegex),
            ],
            asyncValidators: [
              CustomValidators.validateInputAsync(
                (value) => this.authService.isEmailAvailable(value),
                'emailError',
              ),
            ],
            updateOn: 'change',
          },
        ],
        name: [
          '',
          [
            Validators.required,
            Validators.minLength(2),
            Validators.maxLength(50),
          ],
        ],
        surname: [
          '',
          [
            Validators.required,
            Validators.minLength(2),
            Validators.maxLength(50),
          ],
        ],
        password: [
          '',
          [
            Validators.required,
            Validators.minLength(9),
            Validators.maxLength(50),
          ],
        ],
        confirmPassword: [
          '',
          [
            Validators.required,
            Validators.minLength(9),
            Validators.maxLength(50),
          ],
        ],
      },
      {
        validators: CustomValidators.confirmPasswordValidator(
          'password',
          'confirmPassword',
        ),
      },
    );
  }

  onRegisterClick() {
    this.isLoading.set(true);
    const email = this.registerForm.value.email;
    const password = this.registerForm.value.password;
    const name = this.registerForm.value.name;
    const surname = this.registerForm.value.surname;
    this.authService
      .register({
        email,
        password,
        name,
        surname,
      })
      .subscribe({
        next: () => {
          this.router.navigateByUrl('/auth/login');
          this.isLoading.set(false);
        },
        error: () => {
          this.registerError.set(true);
          this.isLoading.set(false);
          setTimeout(() => {
            this.registerError.set(false);
          }, 3000);
        },
      });
  }

  onLoginClick() {
    this.router.navigateByUrl('/auth/login');
  }

  onForgotPasswordClick() {
    this.router.navigateByUrl('/auth/forgot-password');
  }
}
