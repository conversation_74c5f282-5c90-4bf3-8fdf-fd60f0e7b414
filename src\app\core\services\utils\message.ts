import { Injectable, signal, TemplateRef, inject } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';

/**
 * Servizio per la gestione e visualizzazione di messaggi toast e loading.
 */
@Injectable({
  providedIn: 'root',
})
export class MessageService {
  private nzMessageService = inject(NzMessageService);
  private translateService = inject(TranslateService);

  private _isLoading = signal<boolean>(false);
  public readonly isLoading = this._isLoading.asReadonly();

  /**
   * Imposta lo stato di loading per i messaggi.
   * @param isLoading true per mostrare loading, false per nascondere
   * @returns void
   */
  setIsLoading(isLoading: boolean): void {
    this._isLoading.set(isLoading);
  }

  /**
   * Mostra un messaggio toast di tipo specificato.
   * @param type Tipo di messaggio (success, info, warning, error, loading, ...)
   * @param content Contenuto del messaggio (stringa o template)
   * @param clearPreviousMessages Se true rimuove i messaggi precedenti
   * @param loadingState Se true mostra loading infinito
   * @returns void
   */
  addMessage(
    type: 'success' | 'info' | 'warning' | 'error' | 'loading' | string,
    content: string | TemplateRef<void>,
    clearPreviousMessages: boolean,
    loadingState?: boolean,
  ): void {
    if (clearPreviousMessages) this.nzMessageService.remove();
    if (typeof content === 'string')
      content = this.translateService.instant(content);

    loadingState
      ? this.nzMessageService.loading(content, { nzDuration: 0 })
      : this.nzMessageService.create(type, content, { nzDuration: 3000 });
  }

  /**
   * Mostra un messaggio di loading personalizzato.
   * @param label Etichetta opzionale da mostrare
   * @param clearPreviousMessages Se true rimuove i messaggi precedenti
   * @returns void
   */
  addLoadingMessage(label?: string, clearPreviousMessages = true): void {
    this.setIsLoading(true);
    if (clearPreviousMessages) this.nzMessageService.remove();
    this.addMessage(
      'loading',
      label
        ? this.translateService.instant(label)
        : this.translateService.instant('loading'),
      true,
      true,
    );
  }

  addErrorMessage(label?: string, clearPreviousMessages = true): void {
    if (clearPreviousMessages) this.nzMessageService.remove();
    this.setIsLoading(false);
    this.addMessage(
      'error',
      label
        ? this.translateService.instant(label)
        : this.translateService.instant('loading'),
      true,
    );
  }

  addSuccessMessage(label?: string, clearPreviousMessages = true): void {
    if (clearPreviousMessages) this.nzMessageService.remove();
    this.setIsLoading(false);

    this.addMessage(
      'success',
      label
        ? this.translateService.instant(label)
        : this.translateService.instant('loading'),
      true,
    );
  }
}
