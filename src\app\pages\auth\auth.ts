import { Location, NgStyle } from '@angular/common';
import { Component, signal, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';

@Component({
  selector: 'app-auth',
  standalone: true,
  imports: [RouterOutlet, NgStyle],
  templateUrl: './auth.html',
  styleUrl: './auth.less',
})
export class AuthComponent {
  private location = inject(Location);

  protected currentUrl = signal<string | undefined>(undefined);
  ngOnInit(): void {
    this.currentUrl.set(this.location.path());
  }
}
