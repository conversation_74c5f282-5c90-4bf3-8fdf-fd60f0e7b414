import {
  HttpClient,
  HttpErrorResponse,
  HttpRequest,
  HttpResponse,
} from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { log } from '@core/utils/logger';
import { FileResponse, IImageSize } from '@models/interfaces/file-upload';
import { NzUploadXHRArgs } from 'ng-zorro-antd/upload';
import { BehaviorSubject, Observable, tap } from 'rxjs';
import { MessageService } from './message';

/**
 * Servizio per la gestione delle immagini e upload di file.
 * Fornisce metodi per caricare immagini e gestire errori di upload.
 */
@Injectable({
  providedIn: 'root',
})
export class ImageService {
  private http = inject(HttpClient);
  private messageService = inject(MessageService);

  public uploadOnError$ = new BehaviorSubject<any>(null);

  // uploadFile(
  //   item: NzUploadXHRArgs,
  //   productId: string
  // ): Observable<IFileProgress> {
  //   let fileProgress: IFileProgress = {
  //     file: {
  //       name: '',
  //       url: ''
  //     },
  //     fileUpload: null,
  //     progress: null
  //   };

  //   fileProgress.fileUpload = item;

  //   const filePath = `${productId}/${item.file.name}`;
  //   const storageRef = of('FILE');
  //   const uploadTask = this.http.post(
  //     'http://localhost/fakeImage/' + filePath,
  //     item.file
  //   );
  //   return new Observable(observer => {
  //     uploadTask
  //       .pipe(
  //         finalize(() => {
  //           storageRef.subscribe(downloadUrl => {
  //             log('STOREAGE REF: ', downloadUrl);
  //             log('DOWNLOAD URL', downloadUrl);
  //             fileProgress.file.url = downloadUrl;
  //             fileProgress.file.name = item.file.name;
  //             item?.onSuccess(
  //               downloadUrl,
  //               fileProgress.fileUpload.file,
  //               downloadUrl
  //             );
  //             log('OBSERVER NEXT FILEPROGRESS', fileProgress);
  //             observer.next(fileProgress);
  //             observer.complete();
  //           });
  //         })
  //       )
  //       .subscribe({
  //         next: result => {
  //           log('RESULT', result);
  //           const event = { percent: 0 };
  //           event.percent = (<NzUploadFile>result).percent;
  //           log('EVENT PERCENT', event.percent);
  //           item?.onProgress(event, fileProgress.fileUpload.file);
  //         },
  //         error: err => {
  //           log('ERROR PROGRESS', err);
  //           item?.onError(err, fileProgress.fileUpload.file);
  //         }
  //       });

  //     // fileProgress.progress = uploadTask.percentageChanges();
  //   });
  // }

  uploadFileLocal(
    item: NzUploadXHRArgs,
    imageLimits: IImageSize,
  ): Observable<NzUploadXHRArgs> {
    const formData = new FormData();
    formData.append('file', <any>item.file);
    formData.append('imageLimits', JSON.stringify(<any>imageLimits));

    const req = new HttpRequest('POST', item?.action, formData, {
      reportProgress: true,
      withCredentials: false,
    });

    return new Observable((observer) => {
      this.http
        .request(req)
        .pipe(
          tap((event: HttpResponse<FileResponse>) => {
            observer.next(item);
            observer.complete();
          }),
        )
        .subscribe({
          next: (event: HttpResponse<FileResponse>) => {
            item.onSuccess(event.body, item.file, event);
            // this.messageService.addSuccessMessage('imageValid');
          },
          error: (err: HttpErrorResponse) => {
            log('ITEM ON ERROR: ', err);
            this.uploadOnError$.next(item);
            this.messageService.addErrorMessage(err.error.limits);
            item.onError('File bigger than 400 x 508 resolution', item.file);
          },
        });
    });
  }
}
