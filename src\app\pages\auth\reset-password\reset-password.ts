import { UpperCasePipe } from '@angular/common';
import { Component, signal, inject } from '@angular/core';
import {
  Form<PERSON>uilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { trackEvent } from '@aptabase/web';
import { AuthService } from '@core/services/http/auth';
import { DestroyService } from '@core/services/utils/destroy';
import { MessageService } from '@core/services/utils/message';
import { CustomValidators } from '@core/validators/custom.validator';
import { TranslateModule } from '@ngx-translate/core';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { InputPasswordComponent } from '@shared/inputs/input-password/input-password';
import { NzFormDirective } from 'ng-zorro-antd/form';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { NzTypographyComponent } from 'ng-zorro-antd/typography';
import { takeUntil } from 'rxjs';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    InputPasswordComponent,
    FormsModule,
    ReactiveFormsModule,
    NzFormDirective,
    TranslateModule,
    UpperCasePipe,
    NzIconDirective,
    SimpleButtonComponent,
    NzSpinComponent,
    NzTypographyComponent,
  ],
  templateUrl: './reset-password.html',
  styleUrl: './reset-password.less',
  providers: [DestroyService],
})
export class ResetPasswordComponent {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private authService = inject(AuthService);
  private route = inject(ActivatedRoute);
  private destroy$ = inject(DestroyService);
  private messageService = inject(MessageService);

  protected baseForm!: FormGroup;
  private token!: string;
  protected isPageLoading = signal<boolean>(true);
  protected isLoading = signal<boolean>(false);
  protected resetPassowrdError = signal<boolean>(false);

  constructor() {
    trackEvent('reset_password_page');
  }

  ngOnInit(): void {
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe((params) => {
        this.token = params['token'];
        if (!this.token) {
          this.router.navigate(['/']);
        } else {
          this.isPageLoading.set(false);
          this.initForm();
        }
      });
  }

  initForm() {
    this.baseForm = this.fb.group(
      {
        password: [
          '',
          [
            Validators.required,
            Validators.minLength(9),
            Validators.maxLength(50),
          ],
        ],
        confirmPassword: [
          '',
          [
            Validators.required,
            Validators.minLength(9),
            Validators.maxLength(50),
          ],
        ],
      },
      {
        validators: CustomValidators.confirmPasswordValidator(
          'password',
          'confirmPassword',
        ),
      },
    );
  }

  onBackToLoginClick() {
    this.router.navigateByUrl('/auth/login');
  }

  onResetPasswordClick() {
    const { password } = this.baseForm.value;
    this.authService.resetPassword(password, this.token).subscribe({
      next: () => {
        this.messageService.addSuccessMessage('RESET.success');
        this.router.navigateByUrl('/auth/login');
        this.isLoading.set(false);
      },
      error: () => {
        this.resetPassowrdError.set(true);
        this.isLoading.set(false);
        setTimeout(() => {
          this.resetPassowrdError.set(false);
        }, 3000);
      },
    });
  }
}
