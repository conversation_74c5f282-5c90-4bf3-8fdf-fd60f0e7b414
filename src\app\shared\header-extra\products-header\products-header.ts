import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { NzSpaceComponent, NzSpaceItemDirective } from 'ng-zorro-antd/space';

@Component({
  selector: 'app-products-header',
  standalone: true,
  imports: [NzSpaceComponent, SimpleButtonComponent, NzSpaceItemDirective],
  templateUrl: './products-header.html',
  styleUrl: './products-header.less',
})
export class ProductsHeaderComponent {
  private router = inject(Router);


  onAddProductClick() {
    this.router.navigateByUrl('/products/create');
  }
}
